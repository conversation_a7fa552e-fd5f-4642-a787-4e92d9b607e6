<!--审批步骤用户选择组件-->
<template>
  <div class="j-approval-step-user-selector">
    <div v-if="!approvalSteps || approvalSteps.length === 0" class="empty-state">
      <a-empty description="暂无审批步骤" />
    </div>
    <div v-else>
      <div v-for="(step, index) in approvalSteps" :key="step.roleCode" class="approval-step">
        <div class="step-header">
          <span class="step-number">步骤 {{ step.sortOrder }}</span>
          <span class="role-name">{{ step.roleName }}</span>
        </div>
        <div class="user-selector">
          <JSelectUserByDeptRole
            v-model:value="step.selectedUsers"
            :deptId="deptId"
            :roleCode="step.roleCode"
            :placeholder="`请选择${step.roleName}的审批人员`"
            @change="handleUserChange(index, $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import JSelectUserByDeptRole from './JSelectUserByDeptRole.vue';

  defineOptions({ name: 'JApprovalStepUserSelector' });

  const props = defineProps({
    value: propTypes.array.def([]),
    // 审批步骤数据
    approvalSteps: {
      type: Array,
      default: () => [],
    },
    // 部门ID
    deptId: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['change', 'update:value']);

  // 内部值
  const internalValue = ref([]);

  // 监听外部值变化
  watch(
    () => props.value,
    (newVal) => {
      internalValue.value = newVal || [];
    },
    { immediate: true }
  );

  // 监听审批步骤变化，初始化用户选择
  watch(
    () => props.approvalSteps,
    (newSteps) => {
      if (newSteps && newSteps.length > 0) {
        // 为每个步骤初始化selectedUsers字段
        newSteps.forEach(step => {
          if (!step.selectedUsers) {
            step.selectedUsers = [];
          }
        });
      }
    },
    { immediate: true, deep: true }
  );

  /**
   * 处理用户选择变化
   */
  function handleUserChange(stepIndex: number, selectedUsers: any[]) {
    console.log(`步骤${stepIndex}用户选择变化:`, selectedUsers);
    
    // 更新对应步骤的选中用户
    if (props.approvalSteps && props.approvalSteps[stepIndex]) {
      props.approvalSteps[stepIndex].selectedUsers = selectedUsers;
    }
    
    // 构建完整的审批步骤数据
    const stepData = props.approvalSteps.map(step => ({
      roleCode: step.roleCode,
      roleName: step.roleName,
      sortOrder: step.sortOrder,
      selectedUsers: step.selectedUsers || [],
    }));
    
    // 发出变化事件
    emit('change', stepData);
    emit('update:value', stepData);
  }
</script>

<style lang="less" scoped>
  .j-approval-step-user-selector {
    .empty-state {
      text-align: center;
      padding: 40px 0;
    }

    .approval-step {
      margin-bottom: 16px;
      padding: 16px;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      background-color: #fafafa;

      &:last-child {
        margin-bottom: 0;
      }

      .step-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .step-number {
          display: inline-block;
          padding: 2px 8px;
          background-color: #1890ff;
          color: white;
          border-radius: 4px;
          font-size: 12px;
          margin-right: 8px;
        }

        .role-name {
          font-weight: 500;
          color: #262626;
        }
      }

      .user-selector {
        :deep(.ant-select) {
          width: 100%;
        }
      }
    }
  }
</style>
