<!--审批步骤用户选择组件-->
<template>
  <div class="j-approval-step-user-selector">
    <div v-if="!approvalSteps || approvalSteps.length === 0" class="empty-state">
      <a-empty description="暂无审批步骤" />
    </div>
    <div v-else class="approval-steps-container">
      <div class="steps-header">
        <a-icon type="audit" class="header-icon" />
        <div class="header-content">
          <div class="header-title">审批流程配置</div>
          <div class="header-subtitle">请为每个审批步骤选择一位审批人员</div>
        </div>
      </div>

      <div class="steps-content">
        <div v-for="(step, index) in approvalSteps" :key="step.roleCode" class="approval-step">
          <div class="step-indicator">
            <div class="step-circle">{{ step.sortOrder }}</div>
            <div v-if="index < approvalSteps.length - 1" class="step-line"></div>
          </div>

          <div class="step-content">
            <div class="step-header">
              <div class="role-info">
                <span class="role-name">{{ step.roleName }}</span>
                <span class="role-desc">第{{ step.sortOrder }}步审批</span>
              </div>
            </div>

            <div class="user-selector">
              <JSelectUserByDeptRole
                v-model:value="step.selectedUsers"
                :roleId="step.roleId"
                :roleCode="step.roleCode"
                :radioSelection="true"
                :placeholder="`请选择${step.roleName}审批人员`"
                @change="handleUserChange(index, $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watch, computed } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import JSelectUserByDeptRole from './JSelectUserByDeptRole.vue';

  defineOptions({ name: 'JApprovalStepUserSelector' });

  const props = defineProps({
    value: propTypes.array.def([]),
    // 审批步骤数据
    approvalSteps: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['change', 'update:value']);

  // 内部值
  const internalValue = ref([]);

  // 监听外部值变化
  watch(
    () => props.value,
    (newVal) => {
      internalValue.value = newVal || [];
    },
    { immediate: true }
  );

  // 监听审批步骤变化，初始化用户选择
  watch(
    () => props.approvalSteps,
    (newSteps) => {
      if (newSteps && newSteps.length > 0) {
        // 为每个步骤初始化selectedUsers字段
        newSteps.forEach(step => {
          if (!step.selectedUsers) {
            step.selectedUsers = [];
          }
        });
      }
    },
    { immediate: true, deep: true }
  );

  /**
   * 处理用户选择变化
   */
  function handleUserChange(stepIndex: number, selectedUsers: any) {
    console.log(`步骤${stepIndex}用户选择变化:`, selectedUsers);

    // 更新对应步骤的选中用户
    if (props.approvalSteps && props.approvalSteps[stepIndex]) {
      // 单选模式：selectedUsers可能是字符串或数组
      let singleUserId = null;
      if (typeof selectedUsers === 'string') {
        singleUserId = selectedUsers;
      } else if (Array.isArray(selectedUsers) && selectedUsers.length > 0) {
        singleUserId = selectedUsers[0];
      }

      // 保存选中的用户ID
      props.approvalSteps[stepIndex].selectedUsers = singleUserId ? [singleUserId] : [];
      props.approvalSteps[stepIndex].selectedUser = singleUserId;
    }

    // 构建完整的审批步骤数据
    const stepData = props.approvalSteps.map(step => ({
      roleId: step.roleId,
      roleCode: step.roleCode,
      roleName: step.roleName,
      sortOrder: step.sortOrder,
      selectedUsers: step.selectedUsers || [],
      selectedUser: step.selectedUser || null,
    }));

    // 发出变化事件
    emit('change', stepData);
    emit('update:value', stepData);
  }
</script>

<style lang="less" scoped>
  .j-approval-step-user-selector {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;

    .empty-state {
      text-align: center;
      padding: 60px 0;
      color: #999;

      :deep(.ant-empty-description) {
        color: #999;
        font-size: 14px;
      }
    }

    .approval-steps-container {
      .steps-header {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 32px;
        padding: 20px 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        color: white;
        text-align: center;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);

        .header-icon {
          font-size: 24px;
          margin-right: 16px;
        }

        .header-content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .header-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            letter-spacing: 0.5px;
          }

          .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
            font-weight: 400;
          }
        }
      }

      .steps-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        max-width: 600px;
        margin: 0 auto;

        .approval-step {
          display: flex;
          width: 100%;
          margin-bottom: 24px;
          position: relative;

          &:last-child {
            margin-bottom: 0;

            .step-indicator .step-line {
              display: none;
            }
          }

          .step-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-right: 24px;
            position: relative;
            min-width: 50px;

            .step-circle {
              width: 42px;
              height: 42px;
              border-radius: 50%;
              background: linear-gradient(135deg, #1890ff, #36cfc9);
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 700;
              font-size: 16px;
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
              z-index: 2;
              position: relative;

              &::before {
                content: '';
                position: absolute;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: linear-gradient(135deg, #1890ff, #36cfc9);
                opacity: 0.2;
                z-index: -1;
              }
            }

            .step-line {
              width: 3px;
              height: 70px;
              background: linear-gradient(to bottom, #1890ff 0%, #36cfc9 50%, #d9d9d9 100%);
              margin-top: 12px;
              border-radius: 2px;
              position: relative;

              &::before {
                content: '';
                position: absolute;
                left: -2px;
                top: 0;
                width: 7px;
                height: 100%;
                background: linear-gradient(to bottom, rgba(24, 144, 255, 0.2), rgba(54, 207, 201, 0.2));
                border-radius: 4px;
              }
            }
          }

          .step-content {
            flex: 1;
            background: white;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 4px;
              background: linear-gradient(90deg, #1890ff, #36cfc9);
              transform: scaleX(0);
              transition: transform 0.3s ease;
            }

            &:hover {
              border-color: #1890ff;
              box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
              transform: translateY(-2px);

              &::before {
                transform: scaleX(1);
              }
            }

            .step-header {
              margin-bottom: 20px;
              text-align: center;

              .role-info {
                .role-name {
                  font-size: 18px;
                  font-weight: 600;
                  color: #262626;
                  display: block;
                  margin-bottom: 8px;
                  letter-spacing: 0.3px;
                }

                .role-desc {
                  font-size: 13px;
                  color: #8c8c8c;
                  background: linear-gradient(135deg, #f0f2f5, #e6f7ff);
                  padding: 4px 12px;
                  border-radius: 20px;
                  display: inline-block;
                  border: 1px solid #e6f7ff;
                  font-weight: 500;
                }
              }
            }

            .user-selector {
              :deep(.ant-select) {
                width: 100%;

                .ant-select-selector {
                  border-radius: 8px;
                  border: 2px solid #e6f7ff;
                  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                  min-height: 42px;
                  padding: 4px 12px;

                  &:hover {
                    border-color: #40a9ff;
                    box-shadow: 0 2px 8px rgba(64, 169, 255, 0.2);
                  }
                }

                &.ant-select-focused .ant-select-selector {
                  border-color: #1890ff;
                  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.15);
                }

                .ant-select-selection-placeholder {
                  color: #bfbfbf;
                  font-style: italic;
                }
              }
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .j-approval-step-user-selector {
      padding: 16px;

      .approval-steps-container {
        .steps-content {
          max-width: 100%;

          .approval-step {
            .step-indicator {
              margin-right: 16px;
              min-width: 40px;

              .step-circle {
                width: 36px;
                height: 36px;
                font-size: 14px;
              }
            }

            .step-content {
              padding: 20px;

              .step-header .role-info .role-name {
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
</style>
