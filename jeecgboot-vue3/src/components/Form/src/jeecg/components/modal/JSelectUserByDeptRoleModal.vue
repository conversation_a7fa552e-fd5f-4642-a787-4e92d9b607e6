<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    title="根据部门角色选择用户"
    width="800px"
    @ok="handleOk"
    destroyOnClose
    @visible-change="visibleChange"
  >
    <a-row>
      <a-col :span="24">
        <BasicTable
          ref="tableRef"
          :columns="columns"
          :scroll="tableScroll"
          v-bind="getBindValue"
          :useSearchForm="true"
          :formConfig="formConfig"
          :api="getUserListByDeptRole"
          :searchInfo="searchInfo"
          :rowSelection="rowSelection"
          :indexColumnProps="indexColumnProps"
          :afterFetch="afterFetch"
          :beforeFetch="beforeFetch"
        >
          <template #tableTitle></template>
        </BasicTable>
      </a-col>
    </a-row>
  </BasicModal>
</template>

<script lang="ts">
  import { defineComponent, ref, unref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, useTable, BasicColumn } from '/@/components/Table';
  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { useSelectBiz } from '/@/components/Form/src/jeecg/hooks/useSelectBiz';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { selectProps } from '/@/components/Form/src/jeecg/props/props';
  import { defHttp } from '/@/utils/http/axios';

  export default defineComponent({
    name: 'JSelectUserByDeptRoleModal',
    components: {
      BasicModal,
      BasicTable: createAsyncComponent(() => import('/@/components/Table/src/BasicTable.vue'), {
        loading: true,
      }),
    },
    props: {
      ...selectProps,
      //选择框标题
      modalTitle: {
        type: String,
        default: '根据角色选择用户',
      },
      // 角色ID
      roleId: {
        type: String,
        default: '',
      },
      // 角色编码
      roleCode: {
        type: String,
        default: '',
      },
      // 是否单选模式
      radioSelection: {
        type: Boolean,
        default: true,
      },
    },
    emits: ['register', 'getSelectResult', 'close'],
    setup(props, { emit, refs }) {
      const tableScroll = ref<any>({ x: false });
      const tableRef = ref();
      const maxHeight = ref(600);

      //注册弹框
      const [register, { closeModal }] = useModalInner(() => {
        if (window.innerWidth < 900) {
          tableScroll.value = { x: 900 };
        } else {
          tableScroll.value = { x: false };
        }
        setTimeout(() => {
          if (tableRef.value) {
            tableRef.value.setSelectedRowKeys(selectValues['value'] || []);
          }
        }, 800);
      });

      const attrs = useAttrs();
      //表格配置
      const config = {
        canResize: false,
        bordered: true,
        size: 'small',
      };
      const getBindValue = Object.assign({}, unref(props), unref(attrs), config, {
        isRadioSelection: props.radioSelection, // 传递正确的单选配置
      });
      const [{ rowSelection, visibleChange, selectValues, indexColumnProps, getSelectResult, handleDeleteSelected, selectRows }] = useSelectBiz(
        getUserListByDeptRole,
        getBindValue,
        emit
      );

      // 表格列配置
      const columns: BasicColumn[] = [
        {
          title: '用户名',
          dataIndex: 'username',
          width: 120,
        },
        {
          title: '姓名',
          dataIndex: 'realname',
          width: 120,
        },
        {
          title: '部门',
          dataIndex: 'orgCodeTxt',
          width: 150,
        },
        {
          title: '手机号',
          dataIndex: 'phone',
          width: 120,
        },
        {
          title: '邮箱',
          dataIndex: 'email',
          width: 150,
        },
      ];

      // 搜索表单配置
      const formConfig = {
        labelWidth: 80,
        schemas: [
          {
            field: 'username',
            label: '用户名',
            component: 'Input',
            colProps: { span: 8 },
          },
          {
            field: 'realname',
            label: '姓名',
            component: 'Input',
            colProps: { span: 8 },
          },
        ],
      };



      // 搜索信息
      const searchInfo = computed(() => {
        return {
          roleId: props.roleId,
          roleCode: props.roleCode,
        };
      });



      /**
       * 根据角色获取用户列表
       */
      async function getUserListByDeptRole(params) {
        try {
          console.log('获取角色用户列表，参数:', {
            ...params,
            roleId: props.roleId,
            roleCode: props.roleCode,
          });

          const response = await defHttp.get({
            url: '/emsdepartrole/emsDepartRole/getUsersByDeptRole',
            params: {
              ...params,
              roleId: props.roleId,
              roleCode: props.roleCode,
            },
          });

          console.log('获取到用户列表:', response);
          return response;
        } catch (error) {
          console.error('获取角色用户列表失败:', error);
          return {
            records: [],
            total: 0,
          };
        }
      }

      // 数据处理
      function afterFetch(data) {
        return data;
      }

      function beforeFetch(params) {
        return params;
      }

      /**
       * 确定选择
       */
      function handleOk() {
        getSelectResult((options, values) => {
          //回传选中的值
          emit('getSelectResult', options, values);
          //关闭弹窗
          closeModal();
        });
      }

      return {
        register,
        tableScroll,
        tableRef,
        maxHeight,
        getBindValue,
        rowSelection,
        visibleChange,
        selectValues,
        indexColumnProps,
        getSelectResult,
        handleDeleteSelected,
        selectRows,
        columns,
        formConfig,
        searchInfo,
        getUserListByDeptRole,
        afterFetch,
        beforeFetch,
        handleOk,
      };
    },
  });
</script>
