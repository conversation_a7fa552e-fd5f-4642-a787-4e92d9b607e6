<!--根据部门角色选择用户组件-->
<template>
  <div class="JSelectUserByDeptRole">
    <JSelectBiz @change="handleSelectChange" @handleOpen="handleOpen" :loading="loadingEcho" v-bind="attrs"></JSelectBiz>
    <!-- update-begin--author:liaozhiyang---date:20240515---for：【QQYUN-9260】必填模式下会影响到弹窗内antd组件的样式 -->
    <a-form-item>
      <JSelectUserByDeptRoleModal
        :rowKey="rowKey"
        @register="regModal"
        @getSelectResult="setValue"
        v-bind="getBindValue"
        :deptId="deptId"
        :roleCode="roleCode"
        @close="handleClose"
      />
    </a-form-item>
    <!-- update-end--author:liaozhiyang---date:20240515---for：【QQYUN-9260】必填模式下会影响到弹窗内antd组件的样式 -->
  </div>
</template>
<script lang="ts">
  import { unref } from 'vue';
  import JSelectUserByDeptRoleModal from './modal/JSelectUserByDeptRoleModal.vue';
  import JSelectBiz from './base/JSelectBiz.vue';
  import { defineComponent, ref, reactive, watchEffect, watch, provide } from 'vue';
  import { useModal } from '/@/components/Modal';
  import { propTypes } from '/@/utils/propTypes';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { SelectValue } from 'ant-design-vue/es/select';
  import { isArray, isString, isObject } from '/@/utils/is';

  export default defineComponent({
    name: 'JSelectUserByDeptRole',
    components: {
      JSelectBiz,
      JSelectUserByDeptRoleModal,
    },
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.array]),
      rowKey: {
        type: String,
        default: 'username',
      },
      labelKey: {
        type: String,
        default: 'realname',
      },
      // 部门ID
      deptId: {
        type: String,
        default: '',
      },
      // 角色编码
      roleCode: {
        type: String,
        default: '',
      },
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, { emit }) {
      const emitData = ref<any[]>();
      //注册model
      const [regModal, { openModal }] = useModal();
      //表单值
      // const [state] = useRuleFormItem(props, 'value', 'change', emitData);
      //下拉框选项值
      const selectOptions = ref<SelectValue>([]);
      //下拉框选中值
      let selectValues = reactive<Recordable>({
        value: [],
        change: false,
      });
      let tempSave: any = [];
      // 是否正在加载回显数据
      const loadingEcho = ref<boolean>(false);
      //下发 selectOptions,xxxBiz组件接收
      provide('selectOptions', selectOptions);
      //下发 selectValues,xxxBiz组件接收
      provide('selectValues', selectValues);
      //下发 loadingEcho,xxxBiz组件接收
      provide('loadingEcho', loadingEcho);

      const tag = ref(false);
      const attrs = useAttrs();
      const getBindValue = Object.assign(
        {},
        unref(props),
        unref(attrs)
      );

      /**
       * 监听value变化
       */
      watch(
        () => props.value,
        () => {
          if (props.value) {
            selectValues.value = isArray(props.value) ? props.value : props.value.split(',');
          } else {
            selectValues.value = [];
          }
        },
        { immediate: true }
      );

      /**
       * 打开弹出框
       */
      function handleOpen() {
        tag.value = true;
        openModal(true, {
          isUpdate: false,
        });
      }

      /**
       * 值改变事件
       */
      function setValue(options, values) {
        if (tag.value) {
          emitData.value = values;
          selectOptions.value = options;
          selectValues.value = values;
          selectValues.change = true;
          tempSave = values;
        }
      }



      /**
       * 下拉框值改变事件
       */
      function handleSelectChange(values) {
        if (values && values.length > 0) {
          emit('change', values);
          emit('update:value', values);
        }
      }

      /**
       * 关闭弹窗
       */
      function handleClose() {
        tag.value = false;
      }

      return {
        attrs,
        selectOptions,
        getBindValue,
        selectValues,
        loadingEcho,
        tag,
        regModal,
        setValue,
        handleOpen,
        handleClose,
        handleSelectChange,
      };
    },
  });
</script>
<style lang="less" scoped>
  // update-begin--author:liaozhiyang---date:20240515---for：【QQYUN-9260】必填模式下会影响到弹窗内antd组件的样式
  .JSelectUserByDeptRole {
    > .ant-form-item {
      display: none;
    }
  }
  // update-end--author:liaozhiyang---date:20240515---for：【QQYUN-9260】必填模式下会影响到弹窗内antd组件的样式
  .j-select-row {
    @width: 82px;

    .left {
      width: calc(100% - @width - 8px);
    }

    .right {
      width: @width;
    }

    .full {
      width: 100%;
    }

    :deep(.ant-select-search__field) {
      display: none !important;
    }
  }
</style>
