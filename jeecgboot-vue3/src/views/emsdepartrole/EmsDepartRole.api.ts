import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/emsdepartrole/emsDepartRole/list',
  save='/emsdepartrole/emsDepartRole/add',
  edit='/emsdepartrole/emsDepartRole/edit',
  deleteOne = '/emsdepartrole/emsDepartRole/delete',
  deleteBatch = '/emsdepartrole/emsDepartRole/deleteBatch',
  importExcel = '/emsdepartrole/emsDepartRole/importExcel',
  exportXls = '/emsdepartrole/emsDepartRole/exportXls',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 根据部门角色获取用户列表
 * @param params
 */
export const getUsersByDeptRole = (params) => {
  return defHttp.get({
    url: '/emsdepartrole/emsDepartRole/getUsersByDeptRole',
    params
  });
}
