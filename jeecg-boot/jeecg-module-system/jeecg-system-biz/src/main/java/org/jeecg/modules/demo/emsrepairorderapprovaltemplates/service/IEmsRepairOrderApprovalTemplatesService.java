package org.jeecg.modules.demo.emsrepairorderapprovaltemplates.service;

import org.jeecg.modules.demo.emsrepairorderapprovaltemplates.entity.EmsRepairOrderApprovalTemplates;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: 审批模板表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
public interface IEmsRepairOrderApprovalTemplatesService extends IService<EmsRepairOrderApprovalTemplates> {

    /**
     * 激活模板，同一部门只能有一个激活模板
     * @param templateId 模板ID
     * @param sysOrgCode 部门编码
     * @return 是否激活成功
     */
    boolean activateTemplate(String templateId, String sysOrgCode);

    /**
     * 获取当前用户所在部门的激活模板
     * @param sysOrgCode 部门编码
     * @return 激活的模板，如果没有则返回null
     */
    EmsRepairOrderApprovalTemplates getCurrentActiveTemplate(String sysOrgCode);

}
