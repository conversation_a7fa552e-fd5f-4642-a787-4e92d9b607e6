package org.jeecg.modules.demo.emsrepairorders.service.impl;

import org.jeecg.modules.demo.emsrepairorders.entity.EmsRepairOrders;
import org.jeecg.modules.demo.emsrepairorders.mapper.EmsRepairOrdersMapper;
import org.jeecg.modules.demo.emsrepairorders.service.IEmsRepairOrdersService;
import org.jeecg.modules.demo.emsrepairorders.dto.EmsRepairOrderCreateDTO;
import org.jeecg.modules.demo.emsrepairorders.constant.RepairOrderStatusConstant;
import org.jeecg.modules.demo.emsrepairorderapprovalsteps.entity.EmsRepairOrderApprovalSteps;
import org.jeecg.modules.demo.emsrepairorderapprovalsteps.service.IEmsRepairOrderApprovalStepsService;
import org.jeecg.modules.demo.emsrepairorderauditlogs.entity.EmsRepairOrderAuditLogs;
import org.jeecg.modules.demo.emsrepairorderauditlogs.service.IEmsRepairOrderAuditLogsService;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.common.util.oConvertUtils;

import java.util.Date;
import java.util.List;

/**
 * @Description: 维修工单表
 * @Author: jeecg-boot
 * @Date:   2025-08-01
 * @Version: V1.0
 */
@Service
@Slf4j
public class EmsRepairOrdersServiceImpl extends ServiceImpl<EmsRepairOrdersMapper, EmsRepairOrders> implements IEmsRepairOrdersService {

    @Autowired
    private IEmsRepairOrderApprovalStepsService approvalStepsService;

    @Autowired
    private IEmsRepairOrderAuditLogsService auditLogsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRepairOrderWithApproval(EmsRepairOrderCreateDTO createDTO) {
        log.info("开始创建维修工单，工单信息: {}", createDTO.getFaultTitle());

        try {
            // 1. 保存维修工单基本信息
            EmsRepairOrders repairOrder = new EmsRepairOrders();
            // 复制基本属性
            repairOrder.setEquipmentId(createDTO.getEquipmentId());
            repairOrder.setReportId(createDTO.getReportId());
            repairOrder.setPrincipalId(createDTO.getPrincipalId());
            repairOrder.setFaultTitle(createDTO.getFaultTitle());
            repairOrder.setFaultDescription(createDTO.getFaultDescription());
            repairOrder.setAttachment(createDTO.getAttachment());
            repairOrder.setFaultAttachment(createDTO.getFaultAttachment());
            repairOrder.setCurrentStatus(RepairOrderStatusConstant.OrderStatus.PENDING);
            repairOrder.setHandleImages(createDTO.getHandleImages());
            repairOrder.setHandleAttachment(createDTO.getHandleAttachment());

            // 保存工单
            this.save(repairOrder);
            String orderId = repairOrder.getId();
            log.info("维修工单保存成功，工单ID: {}", orderId);

            // 2. 创建审批步骤实例
            if (createDTO.getApprovalUserConfig() != null && !createDTO.getApprovalUserConfig().isEmpty()) {
                createApprovalSteps(orderId, createDTO.getApprovalUserConfig());
            }

            // 3. 创建提交工单的审批日志
            createSubmitAuditLog(orderId, createDTO.getReportId());

            log.info("维修工单创建完成，工单ID: {}", orderId);
            return orderId;

        } catch (Exception e) {
            log.error("创建维修工单失败", e);
            throw new RuntimeException("创建维修工单失败: " + e.getMessage());
        }
    }

    /**
     * 创建审批步骤实例
     */
    private void createApprovalSteps(String orderId, List<EmsRepairOrderCreateDTO.ApprovalStepDTO> approvalSteps) {
        log.info("开始创建审批步骤，工单ID: {}, 步骤数量: {}", orderId, approvalSteps.size());

        for (EmsRepairOrderCreateDTO.ApprovalStepDTO stepDTO : approvalSteps) {
            // 获取选中的用户ID（优先使用selectedUser，如果为空则使用selectedUsers的第一个）
            String userId = stepDTO.getSelectedUser();
            if (oConvertUtils.isEmpty(userId) && stepDTO.getSelectedUsers() != null && !stepDTO.getSelectedUsers().isEmpty()) {
                userId = stepDTO.getSelectedUsers().get(0);
            }

            if (oConvertUtils.isNotEmpty(userId)) {
                EmsRepairOrderApprovalSteps approvalStep = new EmsRepairOrderApprovalSteps();
                approvalStep.setOrderId(orderId);
                approvalStep.setStep(stepDTO.getSortOrder().toString());
                approvalStep.setRoleCode(stepDTO.getRoleCode());
                approvalStep.setUserId(userId);
                approvalStep.setStatus(RepairOrderStatusConstant.StepStatus.PENDING);

                approvalStepsService.save(approvalStep);
                log.info("审批步骤创建成功，步骤: {}, 角色: {}, 用户: {}", stepDTO.getSortOrder(), stepDTO.getRoleCode(), userId);
            } else {
                log.warn("审批步骤 {} 没有选择用户，跳过创建", stepDTO.getSortOrder());
            }
        }
    }

    /**
     * 创建提交工单的审批日志
     */
    private void createSubmitAuditLog(String orderId, String reportId) {
        log.info("创建提交工单审批日志，工单ID: {}, 提交人: {}", orderId, reportId);

        EmsRepairOrderAuditLogs auditLog = new EmsRepairOrderAuditLogs();
        auditLog.setOrderId(orderId);
        auditLog.setOperatorId(reportId);
        auditLog.setOperatorType("0"); // 0=发起人
        auditLog.setActionType("1"); // 1=提交工单
        auditLog.setRemark("用户提交维修工单");
        auditLog.setApprovalStep("0"); // 提交阶段为步骤0

        auditLogsService.save(auditLog);
        log.info("提交工单审批日志创建成功");
    }
}
